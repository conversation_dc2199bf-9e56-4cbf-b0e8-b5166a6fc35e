// PayPal One-time Purchase Creation API
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { tier, email, amount, addons } = req.body;

    // Validate required fields
    if (!email || !amount) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email and amount are required' 
      });
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    
    // Forward request to backend
    const response = await fetch(`${backendUrl}/api/paypal/purchases`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tier: tier?.toLowerCase(),
        email,
        amount: parseFloat(amount),
        addons: addons || {}
      })
    });

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json(data);
    }

    return res.status(200).json(data);

  } catch (error) {
    console.error('PayPal purchase creation error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      details: error.message 
    });
  }
}