// Next.js API Proxy untuk Xendit Subscriptions
export const runtime = 'edge';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3000'

    console.log(
      'Proxying request to:',
      `${backendUrl}/api/xendit/subscriptions`,
    )
    console.log(
      'Request body from frontend:',
      JSON.stringify(req.body, null, 2),
    )

    // Cek USD amount yang dikirim ke backend
    if (req.body.usd_amount) {
      console.log(`💰 USD Amount sent to backend: $${req.body.usd_amount}`)
      console.log(`🔄 Backend should convert this to IDR using RATE_IDR`)
    } else {
      console.log('⚠️  WARNING: usd_amount not found in request body!')
    }

    // Add success/failure redirect URLs to request body for proper modal handling
    // Get the current domain from the request headers
    const protocol = req.headers['x-forwarded-proto'] || 'https';
    const host = req.headers.host || req.headers['x-forwarded-host'];
    const baseUrl = process.env.FRONTEND_URL || (host ? `${protocol}://${host}` : 'http://localhost:3001');

    const modifiedBody = {
      ...req.body,
      success_redirect_url: `${baseUrl}/api/xendit/callback?status=success`,
      failure_redirect_url: `${baseUrl}/api/xendit/callback?status=cancel`,
    }

    console.log('Added redirect URLs to request:', {
      success_redirect_url: modifiedBody.success_redirect_url,
      failure_redirect_url: modifiedBody.failure_redirect_url,
    })

        // Get token from cookies (like dashboard.js does)
    const cookieHeader = req.headers.cookie
    let token = null
    
    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      token = cookies.token
    }

    if (!token) {
      return res.status(401).json({ message: 'Authentication required' })
    }

    // Forward request ke backend with all data (including userId)
    const response = await fetch(`${backendUrl}/api/xendit/subscriptions`, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(modifiedBody),
    })

    const data = await response.json()
    const statusCode = response.status

    console.log('Backend response:', JSON.stringify(data, null, 2))

    // Debug: Check if backend used our redirect URLs
    if (data.success && data.data) {
      console.log('🔍 REDIRECT URL COMPARISON:')
      console.log('📤 Frontend sent:', modifiedBody.success_redirect_url)
      console.log(
        '📥 Backend returned:',
        data.data.success_redirect_url || 'Not found in response',
      )

      if (
        data.data.success_redirect_url &&
        data.data.success_redirect_url !== modifiedBody.success_redirect_url
      ) {
        console.log('❌ PROBLEM: Backend IGNORED our redirect URLs!')
        console.log(
          '❌ This causes modal to redirect internally instead of closing!',
        )
      } else if (
        data.data.success_redirect_url === modifiedBody.success_redirect_url
      ) {
        console.log('✅ Backend correctly used our redirect URLs')
      }
    }

    // Cek apakah backend melakukan konversi USD ke IDR dengan benar
    if (data.success && data.data) {
      console.log(`💸 Amount in backend response: ${data.data.amount}`)
      if (req.body.usd_amount && data.data.amount) {
        const expectedIDR = req.body.usd_amount * 15000 // Expected conversion
        console.log(`📤 Frontend sent USD: $${req.body.usd_amount}`)
        console.log(
          `🧮 Expected IDR (${req.body.usd_amount} × 15000): ${expectedIDR.toLocaleString('id-ID')}`,
        )
        console.log(`📥 Backend returned amount: ${data.data.amount}`)

        if (data.data.amount === expectedIDR) {
          console.log('✅ Backend correctly converted USD to IDR!')
        } else if (data.data.amount === req.body.usd_amount) {
          console.log('❌ Backend NOT converting - still using USD amount!')
        } else {
          console.log(
            '⚠️  Backend returned unexpected amount - check conversion logic',
          )
        }
      }
    }

    // Return response dari backend
    return res.status(statusCode).json(data)
  } catch (error) {
    console.error('Proxy error:', error)
    return res.status(500).json({
      success: false,
      message: 'Proxy server error',
      error: error.message,
    })
  }
}
