// Xendit Success Redirect <PERSON><PERSON> - redirects to callback endpoint
export const runtime = 'edge';

export default async function handler(req, res) {
  const { ref, external_id, subscription_id, invoice_id } = req.query

  try {
    console.log('Xendit success redirect received:', {
      ref,
      external_id,
      subscription_id,
      invoice_id,
    })

    // Redirect to our callback endpoint with proper parameters
    const callbackUrl = new URL(
      '/api/xendit/callback',
      req.headers.origin || 'http://localhost:3001',
    )
    callbackUrl.searchParams.set('status', 'success')

    if (ref) callbackUrl.searchParams.set('ref', ref)
    if (external_id) callbackUrl.searchParams.set('external_id', external_id)
    if (subscription_id)
      callbackUrl.searchParams.set('subscription_id', subscription_id)
    if (invoice_id) callbackUrl.searchParams.set('invoice_id', invoice_id)

    console.log('Redirecting to callback:', callbackUrl.toString())

    // Redirect to callback endpoint
    return res.redirect(302, callbackUrl.toString())
  } catch (error) {
    console.error('Xendit success redirect error:', error)

    // Fallback redirect to callback with error
    const errorCallbackUrl = new URL(
      '/api/xendit/callback',
      req.headers.origin || 'http://localhost:3001',
    )
    errorCallbackUrl.searchParams.set('status', 'error')

    return res.redirect(302, errorCallbackUrl.toString())
  }
}
