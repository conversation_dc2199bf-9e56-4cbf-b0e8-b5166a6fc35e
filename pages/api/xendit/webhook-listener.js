// Webhook listener untuk menangkap notifikasi dari backend
// Backend akan POST ke endpoint ini ketika menerima webhook dari Xendit
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const webhookData = req.body;
    console.log('🔔 Webhook received from backend:', JSON.stringify(webhookData, null, 2));

    // Log important fields for debugging
    console.log('🔍 Webhook key fields:', {
      id: webhookData.id,
      external_id: webhookData.external_id,
      invoice_id: webhookData.invoice_id,
      status: webhookData.status,
      type: webhookData.type
    });

    // Debug global state
    console.log('🔍 Global webhook clients state:', {
      exists: !!global.webhookClients,
      count: global.webhookClients ? global.webhookClients.length : 0,
      clients: global.webhookClients ? global.webhookClients.map(c => ({
        id: c.id,
        destroyed: c.res.destroyed,
        writableEnded: c.res.writableEnded
      })) : []
    });

    // Clean up dead connections first
if (global.webhookClients && global.webhookClients.length > 0) {
  const beforeCount = global.webhookClients.length;
  global.webhookClients = global.webhookClients.filter(client => 
    !client.res.destroyed && !client.res.writableEnded
  );
  const afterCount = global.webhookClients.length;
  if (beforeCount !== afterCount) {
    console.log(`🧹 Pre-cleaned ${beforeCount - afterCount} dead connections`);
  }
}

// Broadcast webhook data ke semua client yang sedang menunggu payment
if (global.webhookClients && global.webhookClients.length > 0) {
  console.log(`📡 Broadcasting to ${global.webhookClients.length} connected clients`);
  
  const successfulBroadcasts = [];
  const failedBroadcasts = [];
  
  global.webhookClients.forEach((client, index) => {
    try {
      if (!client.res.destroyed && !client.res.writableEnded) {
        client.res.write(`data: ${JSON.stringify(webhookData)}\n\n`);
        console.log(`✅ Sent webhook to client ${client.id} (index ${index + 1})`);
        successfulBroadcasts.push(client.id);
      } else {
        console.log(`❌ Client ${client.id} connection closed (destroyed: ${client.res.destroyed}, writableEnded: ${client.res.writableEnded})`);
        failedBroadcasts.push(client.id);
      }
    } catch (error) {
      console.error(`💥 Error sending to client ${client.id}:`, error);
      failedBroadcasts.push(client.id);
    }
  });
  
  console.log(`📊 Broadcast results: ${successfulBroadcasts.length} successful, ${failedBroadcasts.length} failed`);
  
  // Clean up failed connections
  if (failedBroadcasts.length > 0) {
    global.webhookClients = global.webhookClients.filter(client => 
      !failedBroadcasts.includes(client.id)
    );
    console.log(`🧹 Cleaned up ${failedBroadcasts.length} failed connections`);
  }
} else {
      console.log('📭 No clients connected to receive webhook');
    }

    return res.status(200).json({ success: true, message: 'Webhook processed and broadcasted' });

  } catch (error) {
    console.error('Webhook listener error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Webhook processing error',
      error: error.message 
    });
  }
}