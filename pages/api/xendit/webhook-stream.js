// Server-Sent Events untuk real-time webhook notifications
export const runtime = 'edge';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Setup SSE headers
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
    'X-Accel-Buffering': 'no' // Disable nginx buffering for SSE
  });

  // Prevent timeout and keep connection alive
  res.socket.setTimeout(0);
  res.socket.setNoDelay(true);
  res.socket.setKeepAlive(true);

  // Initialize global webhook clients array
  if (!global.webhookClients) {
    global.webhookClients = [];
  }

  // Add this client to the list
  const clientId = Date.now();
  const client = { id: clientId, res };
  global.webhookClients.push(client);

  console.log(`SSE client connected: ${clientId}`);

  // Send initial connection message
  res.write(`data: ${JSON.stringify({ type: 'connected', clientId })}\n\n`);

  // Keep connection alive with heartbeat - more frequent
  const heartbeat = setInterval(() => {
    try {
      if (!res.destroyed && !res.writableEnded) {
        res.write(`data: ${JSON.stringify({ type: 'heartbeat', timestamp: Date.now() })}\n\n`);
        console.log(`💓 Heartbeat sent to client ${clientId}`);
      } else {
        console.log(`🛑 Client ${clientId} connection is destroyed/ended, stopping heartbeat`);
        clearInterval(heartbeat);
        // Remove client from global list
        global.webhookClients = global.webhookClients.filter(c => c.id !== clientId);
      }
    } catch (error) {
      console.error(`❌ Heartbeat error for client ${clientId}:`, error);
      clearInterval(heartbeat);
      // Remove client from global list
      global.webhookClients = global.webhookClients.filter(c => c.id !== clientId);
    }
  }, 15000); // 15 seconds instead of 30

  // Handle client disconnect - consolidated into one listener
  req.on('close', () => {
    console.log(`SSE client disconnected: ${clientId}`);
    clearInterval(heartbeat);
    global.webhookClients = global.webhookClients.filter(c => c.id !== clientId);
  });

  // Handle connection errors
  req.on('error', (error) => {
    console.error(`SSE client error for ${clientId}:`, error);
    clearInterval(heartbeat);
    global.webhookClients = global.webhookClients.filter(c => c.id !== clientId);
  });
}